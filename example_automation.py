#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交通运输网站自动化登录示例脚本
演示如何使用基础版和增强版自动化工具
"""

import os
import sys
import time
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from transportation_login_automation import TransportationLoginAutomator
    from enhanced_transportation_login import EnhancedTransportationLoginAutomator
    import captcha_solver
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有必要的文件都在当前目录中")
    sys.exit(1)


def example_basic_automation():
    """基础版自动化示例"""
    print("🔥 基础版自动化示例")
    print("-" * 50)
    
    try:
        # 创建基础版自动化器
        automator = TransportationLoginAutomator(
            username="",  # 留空，运行时输入
            password="",  # 留空，运行时输入
            headless=False  # 显示浏览器窗口
        )
        
        print("正在运行基础版自动化...")
        success = automator.run_automation()
        
        if success:
            print("✅ 基础版自动化成功!")
        else:
            print("❌ 基础版自动化失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 基础版自动化异常: {e}")
        return False


def example_enhanced_automation():
    """增强版自动化示例"""
    print("\n🚀 增强版自动化示例")
    print("-" * 50)
    
    try:
        # 自定义配置
        config_override = {
            'captcha_config': {
                'similarity_threshold': 0.7,  # 提高相似度阈值
                'max_retry_attempts': 5,      # 增加重试次数
                'save_debug_images': True,    # 保存调试图像
                'verbose': True               # 详细输出
            },
            'login_flow_config': {
                'step_delay': 3,              # 增加步骤间延迟
                'max_retries': 5,             # 增加重试次数
                'screenshot_on_error': True,  # 错误时截图
                'keep_browser_open': True     # 保持浏览器打开
            },
            'website_config': {
                'timeout': 45                 # 增加超时时间
            }
        }
        
        # 创建增强版自动化器
        automator = EnhancedTransportationLoginAutomator(
            username="",  # 留空，运行时输入
            password="",  # 留空，运行时输入
            headless=False,  # 显示浏览器窗口
            config_override=config_override
        )
        
        print("正在运行增强版自动化...")
        success = automator.run_automation_enhanced()
        
        if success:
            print("✅ 增强版自动化成功!")
        else:
            print("❌ 增强版自动化失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 增强版自动化异常: {e}")
        return False


def example_captcha_testing():
    """CAPTCHA解决器测试示例"""
    print("\n🧩 CAPTCHA解决器测试示例")
    print("-" * 50)
    
    # 检查是否有测试HTML文件
    test_files = ['1.html', 'test.html', 'captcha_test.html']
    html_file = None
    
    for file in test_files:
        if os.path.exists(file):
            html_file = file
            break
    
    if not html_file:
        print("⚠️ 未找到测试HTML文件，跳过CAPTCHA测试")
        print("提示：可以将包含CAPTCHA的HTML页面保存为 '1.html' 进行测试")
        return False
    
    try:
        print(f"使用测试文件: {html_file}")
        
        # 测试CAPTCHA解决
        result = captcha_solver.solve_captcha(
            html_file,
            use_gpu=False,
            similarity_threshold=0.6,
            save_debug_images=True,
            verbose=True
        )
        
        if result['success']:
            print("✅ CAPTCHA解决成功!")
            print(f"需要点击的字符: {result['required_chars']}")
            print(f"点击坐标: {result['coordinates']}")
            print(f"成功率: {result['statistics']['success_rate']:.1%}")
            print(f"处理时间: {result['processing_time']:.2f}秒")
        else:
            print(f"❌ CAPTCHA解决失败: {result['error']}")
            
        return result['success']
        
    except Exception as e:
        print(f"❌ CAPTCHA测试异常: {e}")
        return False


def example_custom_configuration():
    """自定义配置示例"""
    print("\n⚙️ 自定义配置示例")
    print("-" * 50)
    
    # 创建自定义配置
    custom_config = {
        'website_config': {
            'timeout': 60,
            'page_load_timeout': 120
        },
        'captcha_config': {
            'use_gpu': False,
            'similarity_threshold': 0.5,  # 降低阈值以提高匹配率
            'max_retry_attempts': 10,     # 增加重试次数
            'save_debug_images': True,
            'verbose': True
        },
        'login_flow_config': {
            'step_delay': 5,              # 更长的延迟
            'input_delay': 0.2,           # 更慢的输入速度
            'click_delay': 1.0,           # 更长的点击延迟
            'max_retries': 10,            # 更多重试
            'screenshot_on_error': True,
            'keep_browser_open': True
        }
    }
    
    print("自定义配置内容:")
    for section, config in custom_config.items():
        print(f"  {section}:")
        for key, value in config.items():
            print(f"    {key}: {value}")
    
    try:
        # 使用自定义配置创建自动化器
        automator = EnhancedTransportationLoginAutomator(
            username="",
            password="",
            headless=False,
            config_override=custom_config
        )
        
        print("\n使用自定义配置运行自动化...")
        success = automator.run_automation_enhanced()
        
        if success:
            print("✅ 自定义配置自动化成功!")
        else:
            print("❌ 自定义配置自动化失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 自定义配置自动化异常: {e}")
        return False


def example_error_handling():
    """错误处理示例"""
    print("\n🛡️ 错误处理示例")
    print("-" * 50)
    
    try:
        # 故意使用错误的URL来演示错误处理
        config_override = {
            'website_config': {
                'base_url': 'https://nonexistent-website-12345.com',
                'timeout': 10
            },
            'error_handling_config': {
                'screenshot_on_error': True,
                'save_page_source': True,
                'max_error_screenshots': 5
            }
        }
        
        automator = EnhancedTransportationLoginAutomator(
            username="test",
            password="test",
            headless=True,  # 使用无头模式
            config_override=config_override
        )
        
        print("正在演示错误处理（使用无效URL）...")
        success = automator.run_automation_enhanced()
        
        print(f"预期结果：失败 - 实际结果：{'成功' if success else '失败'}")
        print("✅ 错误处理机制正常工作")
        
        return True
        
    except Exception as e:
        print(f"错误处理示例异常: {e}")
        print("✅ 异常处理机制正常工作")
        return True


def main():
    """主函数"""
    print("🎯 交通运输网站自动化登录示例")
    print("=" * 80)
    
    examples = [
        ("基础版自动化", example_basic_automation),
        ("增强版自动化", example_enhanced_automation),
        ("CAPTCHA解决器测试", example_captcha_testing),
        ("自定义配置", example_custom_configuration),
        ("错误处理", example_error_handling)
    ]
    
    print("可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"  {i}. {name}")
    
    print("\n选择要运行的示例:")
    print("  0. 运行所有示例")
    print("  1-5. 运行特定示例")
    print("  q. 退出")
    
    try:
        choice = input("\n请输入选择 (0-5 或 q): ").strip().lower()
        
        if choice == 'q':
            print("👋 再见!")
            return
        
        if choice == '0':
            print("\n🚀 运行所有示例...")
            results = []
            for name, func in examples:
                print(f"\n{'='*20} {name} {'='*20}")
                try:
                    result = func()
                    results.append((name, result))
                except KeyboardInterrupt:
                    print(f"\n⚠️ 用户中断了 {name}")
                    break
                except Exception as e:
                    print(f"\n❌ {name} 运行异常: {e}")
                    results.append((name, False))
            
            print("\n" + "="*80)
            print("📊 所有示例运行结果:")
            for name, result in results:
                status = "✅ 成功" if result else "❌ 失败"
                print(f"  {name}: {status}")
        
        elif choice.isdigit() and 1 <= int(choice) <= len(examples):
            index = int(choice) - 1
            name, func = examples[index]
            print(f"\n🚀 运行示例: {name}")
            print("="*80)
            
            try:
                result = func()
                status = "✅ 成功" if result else "❌ 失败"
                print(f"\n📊 示例结果: {status}")
            except KeyboardInterrupt:
                print(f"\n⚠️ 用户中断了示例")
            except Exception as e:
                print(f"\n❌ 示例运行异常: {e}")
        
        else:
            print("❌ 无效选择")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
    
    print("\n🎉 示例演示完成!")


if __name__ == "__main__":
    main()

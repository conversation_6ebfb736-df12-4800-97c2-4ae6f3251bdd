# 中国政府交通运输网站自动化登录工具

这是一个使用Selenium WebDriver自动化登录中国政府交通运输网站的Python工具，集成了先进的CAPTCHA验证码自动解决功能。

## 功能特性

### 🚀 核心功能
- **自动导航**: 自动打开交通运输网站并导航到登录页面
- **智能点击**: 自动点击"营运车辆"→"年审申请"→"法人用户登录"
- **凭据输入**: 安全输入用户名和密码
- **CAPTCHA解决**: 集成先进的OCR技术自动解决验证码
- **登录验证**: 自动验证登录是否成功

### 🛡️ 增强特性
- **多重选择器**: 使用多个CSS/XPath选择器确保元素定位成功
- **智能重试**: 自动重试失败的操作
- **错误处理**: 完善的错误处理和日志记录
- **截图调试**: 自动截图保存关键步骤和错误状态
- **配置驱动**: 支持配置文件自定义所有参数

## 文件结构

```
├── transportation_login_automation.py    # 基础版自动化脚本
├── enhanced_transportation_login.py      # 增强版自动化脚本
├── config.py                            # 配置文件
├── captcha_solver.py                    # CAPTCHA解决器（已存在）
├── example_usage.py                     # CAPTCHA使用示例（已存在）
├── requirements.txt                     # 依赖库列表
└── README.md                           # 说明文档
```

## 安装依赖

### 1. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 2. 安装Chrome浏览器
确保系统已安装Chrome浏览器，脚本会自动下载对应的ChromeDriver。

### 3. 验证CAPTCHA解决器依赖
CAPTCHA解决器需要以下依赖（通常已包含在requirements.txt中）：
```bash
pip install paddleocr pillow opencv-python beautifulsoup4 numpy lxml
```

## 使用方法

### 基础版使用

#### 交互式运行
```bash
python transportation_login_automation.py
```
程序会提示输入用户名、密码和其他选项。

#### 编程方式使用
```python
from transportation_login_automation import TransportationLoginAutomator

# 创建自动化器
automator = TransportationLoginAutomator(
    username="your_username",
    password="your_password",
    headless=False  # 是否无头模式
)

# 运行自动化
success = automator.run_automation()
if success:
    print("登录成功!")
else:
    print("登录失败!")
```

### 增强版使用

#### 命令行运行
```bash
# 基本使用
python enhanced_transportation_login.py

# 指定用户名和密码
python enhanced_transportation_login.py --username your_username --password your_password

# 无头模式运行
python enhanced_transportation_login.py --headless

# 使用自定义配置
python enhanced_transportation_login.py --config custom_config.json

# 详细日志模式
python enhanced_transportation_login.py --verbose
```

#### 编程方式使用
```python
from enhanced_transportation_login import EnhancedTransportationLoginAutomator

# 创建增强版自动化器
automator = EnhancedTransportationLoginAutomator(
    username="your_username",
    password="your_password",
    headless=False,
    config_override={
        'captcha_config': {
            'similarity_threshold': 0.7,
            'max_retry_attempts': 5
        }
    }
)

# 运行增强版自动化
success = automator.run_automation_enhanced()
```

## 配置说明

### 主要配置项

#### 网站配置 (WEBSITE_CONFIG)
```python
WEBSITE_CONFIG = {
    'base_url': 'https://ysfw.mot.gov.cn/NetRoadCGSS-web/',
    'timeout': 30,
    'page_load_timeout': 60,
    'implicit_wait': 10
}
```

#### CAPTCHA配置 (CAPTCHA_CONFIG)
```python
CAPTCHA_CONFIG = {
    'use_gpu': False,              # 是否使用GPU加速
    'similarity_threshold': 0.6,   # 字符匹配相似度阈值
    'save_debug_images': True,     # 是否保存调试图像
    'verbose': True,               # 是否显示详细输出
    'max_retry_attempts': 3,       # 最大重试次数
    'retry_delay': 2               # 重试延迟（秒）
}
```

#### 登录流程配置 (LOGIN_FLOW_CONFIG)
```python
LOGIN_FLOW_CONFIG = {
    'step_delay': 2,               # 每步之间的延迟（秒）
    'input_delay': 0.1,            # 输入字符间延迟（秒）
    'click_delay': 0.5,            # 点击后延迟（秒）
    'max_retries': 3,              # 最大重试次数
    'screenshot_on_error': True,   # 出错时是否截图
    'keep_browser_open': True      # 完成后是否保持浏览器打开
}
```

### 自定义配置文件

创建JSON格式的配置文件来覆盖默认设置：

```json
{
    "captcha_config": {
        "similarity_threshold": 0.7,
        "max_retry_attempts": 5,
        "use_gpu": true
    },
    "login_flow_config": {
        "step_delay": 3,
        "max_retries": 5
    },
    "website_config": {
        "timeout": 45
    }
}
```

## CAPTCHA解决器集成

### 工作原理
1. **HTML解析**: 从页面HTML中提取CAPTCHA图像和指令文本
2. **OCR识别**: 使用PaddleOCR进行字符识别
3. **坐标匹配**: 智能匹配字符并计算点击坐标
4. **自动点击**: 使用Selenium执行精确的坐标点击

### 使用示例
```python
import captcha_solver

# 从HTML内容解决CAPTCHA
html_content = driver.page_source
result = captcha_solver.solve_captcha_from_html(html_content)

if result['success']:
    coordinates = result['coordinates']
    required_chars = result['required_chars']
    print(f"需要点击的字符: {required_chars}")
    print(f"点击坐标: {coordinates}")
```

## 故障排除

### 常见问题

#### 1. ChromeDriver问题
```
WebDriverException: 'chromedriver' executable needs to be in PATH
```
**解决方案**: 脚本会自动下载ChromeDriver，确保网络连接正常。

#### 2. 元素定位失败
```
TimeoutException: Message: 
```
**解决方案**: 
- 检查网站是否可访问
- 增加timeout配置
- 检查选择器是否需要更新

#### 3. CAPTCHA解决失败
```
CAPTCHA解决失败: 无法从HTML内容中提取CAPTCHA图像
```
**解决方案**:
- 检查页面是否真的有CAPTCHA
- 降低similarity_threshold
- 启用save_debug_images查看调试图像

#### 4. 登录验证失败
```
登录失败: 登录状态不明确
```
**解决方案**:
- 检查用户名密码是否正确
- 更新SUCCESS_CHECK_CONFIG中的成功指示器
- 手动检查登录后的页面特征

### 调试技巧

#### 1. 启用详细日志
```bash
python enhanced_transportation_login.py --verbose
```

#### 2. 保存调试截图
```python
automator.take_screenshot("debug_step1")
```

#### 3. 检查页面源码
错误时会自动保存页面源码到HTML文件。

#### 4. 调整CAPTCHA参数
```python
config_override = {
    'captcha_config': {
        'similarity_threshold': 0.5,  # 降低阈值
        'save_debug_images': True,    # 保存调试图像
        'verbose': True               # 详细输出
    }
}
```

## 注意事项

### ⚠️ 重要提醒
1. **合法使用**: 仅用于合法的自动化测试和个人使用
2. **频率控制**: 避免过于频繁的请求，以免被网站限制
3. **凭据安全**: 不要在代码中硬编码敏感信息
4. **网站变更**: 网站结构变更可能需要更新选择器

### 🔒 安全建议
1. 使用环境变量存储敏感信息
2. 定期更新依赖库
3. 在测试环境中验证脚本
4. 遵守网站的使用条款

## 许可证

本项目仅供学习和研究使用。请遵守相关法律法规和网站使用条款。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 更新日志

### v1.0.0
- 初始版本发布
- 基础自动化功能
- CAPTCHA解决器集成

### v2.0.0 (增强版)
- 配置文件驱动
- 增强的错误处理
- 多重选择器支持
- 详细的日志记录
- 命令行参数支持

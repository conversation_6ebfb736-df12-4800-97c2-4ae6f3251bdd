#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国政府交通运输网站自动登录脚本
使用Selenium WebDriver自动化登录流程，集成CAPTCHA验证码解决器

功能特性：
1. 自动导航到交通运输网站
2. 点击"营运车辆"菜单
3. 点击"年审申请"选项
4. 选择"法人用户登录"
5. 输入用户名和密码
6. 自动解决CAPTCHA验证码
7. 完成登录流程

依赖库：
    pip install selenium webdriver-manager
    
使用方法：
    python transportation_login_automation.py
"""

import time
import logging
import os
import tempfile
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    WebDriverException,
    ElementClickInterceptedException
)
from webdriver_manager.chrome import ChromeDriverManager

# 导入现有的CAPTCHA解决器
import captcha_solver

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('transportation_login.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TransportationLoginAutomator:
    """交通运输网站登录自动化器"""
    
    def __init__(self, username: str = "", password: str = "", headless: bool = False):
        """
        初始化自动化器
        
        Args:
            username: 登录用户名
            password: 登录密码
            headless: 是否使用无头模式
        """
        self.username = username
        self.password = password
        self.headless = headless
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        
        # 网站URL和选择器配置
        self.base_url = "https://ysfw.mot.gov.cn/NetRoadCGSS-web/"
        self.timeout = 30
        
        # 常用选择器
        self.selectors = {
            'commercial_vehicles': [
                "//a[contains(text(), '营运车辆')]",
                "//span[contains(text(), '营运车辆')]",
                "//div[contains(text(), '营运车辆')]",
                "//li[contains(text(), '营运车辆')]"
            ],
            'annual_inspection': [
                "//a[contains(text(), '年审申请')]",
                "//span[contains(text(), '年审申请')]",
                "//div[contains(text(), '年审申请')]",
                "//li[contains(text(), '年审申请')]"
            ],
            'corporate_login': [
                "//a[contains(text(), '法人用户登录')]",
                "//span[contains(text(), '法人用户登录')]",
                "//div[contains(text(), '法人用户登录')]",
                "//button[contains(text(), '法人用户登录')]"
            ],
            'username_field': [
                "//input[@type='text' and (@name='username' or @id='username' or @placeholder*='用户名')]",
                "//input[@type='text' and contains(@class, 'username')]",
                "//input[@name='loginName']",
                "//input[@id='loginName']"
            ],
            'password_field': [
                "//input[@type='password']",
                "//input[@name='password']",
                "//input[@id='password']"
            ],
            'captcha_image': [
                "//img[contains(@src, 'captcha') or contains(@src, 'verify') or contains(@id, 'captcha')]",
                "//img[contains(@class, 'captcha')]"
            ],
            'login_button': [
                "//button[contains(text(), '登录') or contains(text(), '登陆')]",
                "//input[@type='submit' and (@value='登录' or @value='登陆')]",
                "//a[contains(text(), '登录') or contains(text(), '登陆')]"
            ]
        }
        
    def setup_driver(self) -> bool:
        """
        设置Chrome WebDriver
        
        Returns:
            bool: 设置是否成功
        """
        try:
            logger.info("正在设置Chrome WebDriver...")
            
            # Chrome选项配置
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            
            # 优化选项
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            
            # 禁用图片加载以提高速度（可选）
            # prefs = {"profile.managed_default_content_settings.images": 2}
            # chrome_options.add_experimental_option("prefs", prefs)
            
            # 自动下载ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            # 创建WebDriver实例
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, self.timeout)
            
            logger.info("Chrome WebDriver设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置Chrome WebDriver失败: {e}")
            return False
    
    def navigate_to_website(self) -> bool:
        """
        导航到交通运输网站
        
        Returns:
            bool: 导航是否成功
        """
        try:
            logger.info(f"正在导航到网站: {self.base_url}")
            self.driver.get(self.base_url)
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            logger.info("成功导航到网站")
            return True
            
        except TimeoutException:
            logger.error("网站加载超时")
            return False
        except Exception as e:
            logger.error(f"导航到网站失败: {e}")
            return False
    
    def find_and_click_element(self, selectors: list, description: str, timeout: int = None) -> bool:
        """
        查找并点击元素（尝试多个选择器）
        
        Args:
            selectors: 选择器列表
            description: 元素描述
            timeout: 超时时间
            
        Returns:
            bool: 点击是否成功
        """
        if timeout is None:
            timeout = self.timeout
            
        logger.info(f"正在查找并点击: {description}")
        
        for selector in selectors:
            try:
                # 等待元素可点击
                element = WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                
                # 滚动到元素位置
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)
                
                # 尝试点击
                try:
                    element.click()
                except ElementClickInterceptedException:
                    # 如果普通点击失败，使用JavaScript点击
                    self.driver.execute_script("arguments[0].click();", element)
                
                logger.info(f"成功点击: {description}")
                return True
                
            except TimeoutException:
                logger.debug(f"选择器超时: {selector}")
                continue
            except Exception as e:
                logger.debug(f"选择器失败 {selector}: {e}")
                continue
        
        logger.error(f"无法找到或点击: {description}")
        return False

    def input_credentials(self) -> bool:
        """
        输入登录凭据

        Returns:
            bool: 输入是否成功
        """
        try:
            # 输入用户名
            if not self.username:
                self.username = input("请输入用户名: ")

            logger.info("正在输入用户名...")
            username_element = None
            for selector in self.selectors['username_field']:
                try:
                    username_element = self.wait.until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if not username_element:
                logger.error("无法找到用户名输入框")
                return False

            username_element.clear()
            username_element.send_keys(self.username)

            # 输入密码
            if not self.password:
                import getpass
                self.password = getpass.getpass("请输入密码: ")

            logger.info("正在输入密码...")
            password_element = None
            for selector in self.selectors['password_field']:
                try:
                    password_element = self.wait.until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if not password_element:
                logger.error("无法找到密码输入框")
                return False

            password_element.clear()
            password_element.send_keys(self.password)

            logger.info("成功输入登录凭据")
            return True

        except Exception as e:
            logger.error(f"输入登录凭据失败: {e}")
            return False

    def handle_captcha(self) -> bool:
        """
        处理CAPTCHA验证码

        Returns:
            bool: CAPTCHA处理是否成功
        """
        try:
            logger.info("正在处理CAPTCHA验证码...")

            # 查找CAPTCHA图像
            captcha_element = None
            for selector in self.selectors['captcha_image']:
                try:
                    captcha_element = self.wait.until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if not captcha_element:
                logger.warning("未找到CAPTCHA图像，可能不需要验证码")
                return True

            # 获取页面HTML内容
            page_html = self.driver.page_source

            # 使用captcha_solver解决验证码
            logger.info("正在使用CAPTCHA解决器分析验证码...")
            result = captcha_solver.solve_captcha_from_html(
                page_html,
                use_gpu=False,
                similarity_threshold=0.6,
                save_debug_images=True,
                verbose=True
            )

            if not result['success']:
                logger.error(f"CAPTCHA解决失败: {result.get('error', '未知错误')}")
                return False

            # 获取点击坐标
            coordinates = result['coordinates']
            required_chars = result['required_chars']

            logger.info(f"CAPTCHA解决成功! 需要点击字符: {required_chars}")
            logger.info(f"点击坐标: {coordinates}")

            # 执行点击操作
            for i, coord in enumerate(coordinates):
                if coord:
                    x, y = coord
                    logger.info(f"点击第{i+1}个字符 '{required_chars[i]}' 坐标: ({x}, {y})")

                    # 使用ActionChains进行精确点击
                    from selenium.webdriver.common.action_chains import ActionChains
                    actions = ActionChains(self.driver)

                    # 相对于CAPTCHA图像的点击
                    actions.move_to_element_with_offset(captcha_element, x, y).click().perform()
                    time.sleep(0.5)  # 短暂延迟
                else:
                    logger.warning(f"第{i+1}个字符坐标缺失，跳过")

            logger.info("CAPTCHA验证码处理完成")
            return True

        except Exception as e:
            logger.error(f"处理CAPTCHA验证码失败: {e}")
            return False

    def complete_login(self) -> bool:
        """
        完成登录过程

        Returns:
            bool: 登录是否成功
        """
        try:
            logger.info("正在提交登录表单...")

            # 点击登录按钮
            if not self.find_and_click_element(self.selectors['login_button'], "登录按钮"):
                return False

            # 等待登录结果
            time.sleep(3)

            # 检查是否登录成功（可以根据实际页面调整检查逻辑）
            current_url = self.driver.current_url
            page_title = self.driver.title

            # 简单的成功检查（可以根据实际情况调整）
            success_indicators = [
                "主页", "首页", "dashboard", "main", "index",
                "欢迎", "welcome", "用户中心", "个人中心"
            ]

            is_success = any(indicator in page_title.lower() or indicator in current_url.lower()
                           for indicator in success_indicators)

            if is_success:
                logger.info("登录成功!")
                logger.info(f"当前页面: {page_title}")
                logger.info(f"当前URL: {current_url}")
                return True
            else:
                # 检查是否有错误信息
                try:
                    error_selectors = [
                        "//div[contains(@class, 'error')]",
                        "//span[contains(@class, 'error')]",
                        "//div[contains(text(), '错误') or contains(text(), '失败')]"
                    ]

                    for selector in error_selectors:
                        try:
                            error_element = self.driver.find_element(By.XPATH, selector)
                            if error_element.is_displayed():
                                error_text = error_element.text
                                logger.error(f"登录失败，错误信息: {error_text}")
                                return False
                        except NoSuchElementException:
                            continue

                    logger.warning("登录状态不明确，请手动检查")
                    return False

                except Exception as e:
                    logger.warning(f"检查登录状态时出错: {e}")
                    return False

        except Exception as e:
            logger.error(f"完成登录失败: {e}")
            return False

    def run_automation(self) -> bool:
        """
        运行完整的自动化登录流程

        Returns:
            bool: 自动化是否成功
        """
        try:
            logger.info("开始交通运输网站自动化登录流程")
            logger.info("=" * 60)

            # 步骤1: 设置WebDriver
            if not self.setup_driver():
                return False

            # 步骤2: 导航到网站
            if not self.navigate_to_website():
                return False

            # 步骤3: 点击"营运车辆"
            time.sleep(2)  # 等待页面完全加载
            if not self.find_and_click_element(
                self.selectors['commercial_vehicles'],
                "营运车辆菜单"
            ):
                return False

            # 步骤4: 点击"年审申请"
            time.sleep(2)
            if not self.find_and_click_element(
                self.selectors['annual_inspection'],
                "年审申请选项"
            ):
                return False

            # 步骤5: 点击"法人用户登录"
            time.sleep(2)
            if not self.find_and_click_element(
                self.selectors['corporate_login'],
                "法人用户登录"
            ):
                return False

            # 步骤6: 输入登录凭据
            time.sleep(2)
            if not self.input_credentials():
                return False

            # 步骤7: 处理CAPTCHA验证码
            if not self.handle_captcha():
                return False

            # 步骤8: 完成登录
            if not self.complete_login():
                return False

            logger.info("自动化登录流程完成!")
            return True

        except Exception as e:
            logger.error(f"自动化流程失败: {e}")
            return False
        finally:
            # 可选：保持浏览器打开以供用户查看结果
            if not self.headless:
                input("按Enter键关闭浏览器...")
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver已关闭")
        except Exception as e:
            logger.warning(f"清理资源时出错: {e}")

    def take_screenshot(self, filename: str = None) -> str:
        """
        截取当前页面截图

        Args:
            filename: 截图文件名

        Returns:
            str: 截图文件路径
        """
        try:
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}.png"

            screenshot_path = os.path.join(os.getcwd(), filename)
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"截图已保存: {screenshot_path}")
            return screenshot_path

        except Exception as e:
            logger.error(f"截图失败: {e}")
            return ""

    def wait_for_page_load(self, timeout: int = None) -> bool:
        """
        等待页面加载完成

        Args:
            timeout: 超时时间

        Returns:
            bool: 页面是否加载完成
        """
        if timeout is None:
            timeout = self.timeout

        try:
            # 等待页面加载完成
            self.wait.until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            return True
        except TimeoutException:
            logger.warning("页面加载超时")
            return False


def main():
    """主函数"""
    print("🚗 中国政府交通运输网站自动化登录工具")
    print("=" * 60)

    # 获取用户输入
    username = input("请输入用户名 (留空稍后输入): ").strip()
    password = input("请输入密码 (留空稍后输入): ").strip()

    # 询问是否使用无头模式
    headless_input = input("是否使用无头模式? (y/N): ").strip().lower()
    headless = headless_input in ['y', 'yes', '是']

    try:
        # 创建自动化器实例
        automator = TransportationLoginAutomator(
            username=username,
            password=password,
            headless=headless
        )

        # 运行自动化流程
        success = automator.run_automation()

        if success:
            print("\n✅ 自动化登录成功!")
        else:
            print("\n❌ 自动化登录失败，请查看日志了解详情")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        logger.error(f"程序异常: {e}", exc_info=True)


if __name__ == "__main__":
    main()

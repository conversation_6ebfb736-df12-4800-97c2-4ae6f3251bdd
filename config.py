#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交通运输网站自动化登录配置文件
包含网站URL、选择器、超时设置等配置项
"""

# 网站配置
WEBSITE_CONFIG = {
    'base_url': 'https://ysfw.mot.gov.cn/NetRoadCGSS-web/',
    'timeout': 30,
    'page_load_timeout': 60,
    'implicit_wait': 10
}

# WebDriver配置
WEBDRIVER_CONFIG = {
    'window_size': '1920,1080',
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'download_images': True,  # 是否下载图片
    'enable_javascript': True,
    'enable_plugins': False
}

# 元素选择器配置
SELECTORS = {
    # 营运车辆菜单
    'commercial_vehicles': [
        "//a[contains(text(), '营运车辆')]",
        "//span[contains(text(), '营运车辆')]",
        "//div[contains(text(), '营运车辆')]",
        "//li[contains(text(), '营运车辆')]",
        "//button[contains(text(), '营运车辆')]",
        "//*[contains(@title, '营运车辆')]",
        "//*[contains(@alt, '营运车辆')]"
    ],
    
    # 年审申请选项
    'annual_inspection': [
        "//a[contains(text(), '年审申请')]",
        "//span[contains(text(), '年审申请')]",
        "//div[contains(text(), '年审申请')]",
        "//li[contains(text(), '年审申请')]",
        "//button[contains(text(), '年审申请')]",
        "//*[contains(@title, '年审申请')]"
    ],
    
    # 法人用户登录
    'corporate_login': [
        "//a[contains(text(), '法人用户登录')]",
        "//span[contains(text(), '法人用户登录')]",
        "//div[contains(text(), '法人用户登录')]",
        "//button[contains(text(), '法人用户登录')]",
        "//a[contains(text(), '企业用户')]",
        "//button[contains(text(), '企业用户')]",
        "//*[contains(@title, '法人用户登录')]"
    ],
    
    # 用户名输入框
    'username_field': [
        "//input[@type='text' and (@name='username' or @id='username' or contains(@placeholder, '用户名'))]",
        "//input[@type='text' and contains(@class, 'username')]",
        "//input[@name='loginName']",
        "//input[@id='loginName']",
        "//input[@name='userName']",
        "//input[@id='userName']",
        "//input[@name='account']",
        "//input[@id='account']",
        "//input[contains(@placeholder, '请输入用户名')]",
        "//input[contains(@placeholder, '账号')]"
    ],
    
    # 密码输入框
    'password_field': [
        "//input[@type='password']",
        "//input[@name='password']",
        "//input[@id='password']",
        "//input[@name='pwd']",
        "//input[@id='pwd']",
        "//input[contains(@placeholder, '密码')]",
        "//input[contains(@placeholder, '请输入密码')]"
    ],
    
    # CAPTCHA验证码图片
    'captcha_image': [
        "//img[contains(@src, 'captcha') or contains(@src, 'verify') or contains(@id, 'captcha')]",
        "//img[contains(@class, 'captcha')]",
        "//img[contains(@alt, 'captcha') or contains(@alt, '验证码')]",
        "//img[contains(@title, 'captcha') or contains(@title, '验证码')]",
        "//canvas[contains(@id, 'captcha')]",
        "//*[contains(@class, 'verify-img')]"
    ],
    
    # CAPTCHA输入框
    'captcha_input': [
        "//input[@name='captcha' or @id='captcha']",
        "//input[contains(@placeholder, '验证码')]",
        "//input[contains(@class, 'captcha')]",
        "//input[@name='verifyCode']",
        "//input[@id='verifyCode']"
    ],
    
    # 登录按钮
    'login_button': [
        "//button[contains(text(), '登录') or contains(text(), '登陆')]",
        "//input[@type='submit' and (@value='登录' or @value='登陆')]",
        "//a[contains(text(), '登录') or contains(text(), '登陆')]",
        "//button[@type='submit']",
        "//input[@type='button' and (@value='登录' or @value='登陆')]",
        "//*[contains(@class, 'login-btn')]",
        "//*[contains(@id, 'login')]"
    ],
    
    # 错误信息
    'error_messages': [
        "//div[contains(@class, 'error')]",
        "//span[contains(@class, 'error')]",
        "//div[contains(text(), '错误') or contains(text(), '失败')]",
        "//div[contains(@class, 'alert')]",
        "//div[contains(@class, 'warning')]",
        "//*[contains(@class, 'message') and contains(@class, 'error')]"
    ],
    
    # 成功登录指示器
    'success_indicators': [
        "//div[contains(text(), '欢迎')]",
        "//span[contains(text(), '欢迎')]",
        "//*[contains(@class, 'welcome')]",
        "//*[contains(@class, 'user-info')]",
        "//a[contains(text(), '退出') or contains(text(), '注销')]"
    ]
}

# CAPTCHA解决器配置
CAPTCHA_CONFIG = {
    'use_gpu': False,
    'similarity_threshold': 0.6,
    'save_debug_images': True,
    'verbose': True,
    'max_retry_attempts': 3,
    'retry_delay': 2  # 秒
}

# 登录流程配置
LOGIN_FLOW_CONFIG = {
    'step_delay': 2,  # 每步之间的延迟（秒）
    'input_delay': 0.1,  # 输入字符间延迟（秒）
    'click_delay': 0.5,  # 点击后延迟（秒）
    'max_retries': 3,  # 最大重试次数
    'screenshot_on_error': True,  # 出错时是否截图
    'keep_browser_open': True  # 完成后是否保持浏览器打开
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'transportation_login.log',
    'encoding': 'utf-8',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# 成功登录检查配置
SUCCESS_CHECK_CONFIG = {
    'url_keywords': [
        'main', 'index', 'dashboard', 'home', 'welcome',
        '主页', '首页', '欢迎'
    ],
    'title_keywords': [
        'main', 'index', 'dashboard', 'home', 'welcome',
        '主页', '首页', '欢迎', '用户中心', '个人中心'
    ],
    'element_indicators': SELECTORS['success_indicators']
}

# 错误处理配置
ERROR_HANDLING_CONFIG = {
    'screenshot_on_error': True,
    'save_page_source': True,
    'max_error_screenshots': 10,
    'error_retry_delay': 5,  # 秒
    'critical_errors': [
        'WebDriverException',
        'TimeoutException',
        'NoSuchElementException'
    ]
}

# 导出所有配置
__all__ = [
    'WEBSITE_CONFIG',
    'WEBDRIVER_CONFIG', 
    'SELECTORS',
    'CAPTCHA_CONFIG',
    'LOGIN_FLOW_CONFIG',
    'LOGGING_CONFIG',
    'SUCCESS_CHECK_CONFIG',
    'ERROR_HANDLING_CONFIG'
]

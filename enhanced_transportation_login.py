#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版中国政府交通运输网站自动化登录脚本
使用配置文件，支持更多自定义选项和错误处理

功能特性：
1. 配置文件驱动的自动化
2. 增强的错误处理和重试机制
3. 详细的日志记录
4. 灵活的元素定位策略
5. CAPTCHA自动解决
6. 截图和调试支持

使用方法：
    python enhanced_transportation_login.py
    python enhanced_transportation_login.py --username your_username --password your_password
    python enhanced_transportation_login.py --headless --config custom_config.py
"""

import argparse
import time
import logging
import os
import sys
import tempfile
import json
from typing import Optional, Dict, Any, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    WebDriverException,
    ElementClickInterceptedException,
    StaleElementReferenceException
)
from webdriver_manager.chrome import ChromeDriverManager

# 导入配置和CAPTCHA解决器
try:
    from config import *
    import captcha_solver
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保 config.py 和 captcha_solver.py 文件存在")
    sys.exit(1)


class EnhancedTransportationLoginAutomator:
    """增强版交通运输网站登录自动化器"""
    
    def __init__(self, username: str = "", password: str = "", headless: bool = False, 
                 config_override: Dict = None):
        """
        初始化增强版自动化器
        
        Args:
            username: 登录用户名
            password: 登录密码
            headless: 是否使用无头模式
            config_override: 配置覆盖字典
        """
        self.username = username
        self.password = password
        self.headless = headless
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        
        # 加载配置
        self.website_config = WEBSITE_CONFIG.copy()
        self.webdriver_config = WEBDRIVER_CONFIG.copy()
        self.selectors = SELECTORS.copy()
        self.captcha_config = CAPTCHA_CONFIG.copy()
        self.login_flow_config = LOGIN_FLOW_CONFIG.copy()
        self.success_check_config = SUCCESS_CHECK_CONFIG.copy()
        self.error_handling_config = ERROR_HANDLING_CONFIG.copy()
        
        # 应用配置覆盖
        if config_override:
            self._apply_config_override(config_override)
        
        # 设置日志
        self._setup_logging()
        
        # 统计信息
        self.stats = {
            'start_time': None,
            'end_time': None,
            'steps_completed': 0,
            'errors_encountered': 0,
            'screenshots_taken': 0,
            'captcha_attempts': 0
        }
        
    def _apply_config_override(self, config_override: Dict):
        """应用配置覆盖"""
        for key, value in config_override.items():
            if hasattr(self, key):
                getattr(self, key).update(value)
    
    def _setup_logging(self):
        """设置日志记录"""
        log_config = LOGGING_CONFIG
        
        # 创建logger
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(getattr(logging, log_config['level']))
        
        # 清除现有handlers
        self.logger.handlers.clear()
        
        # 文件handler
        file_handler = logging.FileHandler(
            log_config['file'], 
            encoding=log_config['encoding']
        )
        file_handler.setLevel(getattr(logging, log_config['level']))
        
        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, log_config['level']))
        
        # 格式化器
        formatter = logging.Formatter(log_config['format'])
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def setup_driver(self) -> bool:
        """
        设置Chrome WebDriver（增强版）
        
        Returns:
            bool: 设置是否成功
        """
        try:
            self.logger.info("正在设置增强版Chrome WebDriver...")
            
            # Chrome选项配置
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument("--headless")
            
            # 基础选项
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument(f"--window-size={self.webdriver_config['window_size']}")
            chrome_options.add_argument(f"--user-agent={self.webdriver_config['user_agent']}")
            
            # 性能优化选项
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-images") if not self.webdriver_config['download_images'] else None
            chrome_options.add_argument("--disable-javascript") if not self.webdriver_config['enable_javascript'] else None
            
            # 安全和隐私选项
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 自动下载ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            # 创建WebDriver实例
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 设置超时
            self.driver.implicitly_wait(self.website_config['implicit_wait'])
            self.driver.set_page_load_timeout(self.website_config['page_load_timeout'])
            
            # 创建WebDriverWait实例
            self.wait = WebDriverWait(self.driver, self.website_config['timeout'])
            
            # 隐藏自动化特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info("增强版Chrome WebDriver设置成功")
            return True
            
        except Exception as e:
            self.logger.error(f"设置Chrome WebDriver失败: {e}")
            self.stats['errors_encountered'] += 1
            return False
    
    def navigate_to_website(self) -> bool:
        """
        导航到交通运输网站（增强版）
        
        Returns:
            bool: 导航是否成功
        """
        try:
            url = self.website_config['base_url']
            self.logger.info(f"正在导航到网站: {url}")
            
            self.driver.get(url)
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # 额外等待确保页面完全加载
            self._wait_for_page_load()
            
            # 记录页面信息
            self.logger.info(f"页面标题: {self.driver.title}")
            self.logger.info(f"当前URL: {self.driver.current_url}")
            
            self.stats['steps_completed'] += 1
            self.logger.info("成功导航到网站")
            return True
            
        except TimeoutException:
            self.logger.error("网站加载超时")
            self.stats['errors_encountered'] += 1
            self._handle_error("网站导航超时")
            return False
        except Exception as e:
            self.logger.error(f"导航到网站失败: {e}")
            self.stats['errors_encountered'] += 1
            self._handle_error(f"网站导航失败: {e}")
            return False
    
    def _wait_for_page_load(self, timeout: int = None) -> bool:
        """
        等待页面完全加载
        
        Args:
            timeout: 超时时间
            
        Returns:
            bool: 页面是否加载完成
        """
        if timeout is None:
            timeout = self.website_config['timeout']
            
        try:
            # 等待document.readyState为complete
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # 等待jQuery加载完成（如果存在）
            try:
                WebDriverWait(self.driver, 5).until(
                    lambda driver: driver.execute_script("return typeof jQuery !== 'undefined' ? jQuery.active == 0 : true")
                )
            except:
                pass  # 如果没有jQuery，忽略
            
            return True
        except TimeoutException:
            self.logger.warning("页面加载超时")
            return False

    def find_and_click_element_enhanced(self, selectors: List[str], description: str,
                                      timeout: int = None, retry_count: int = 3) -> bool:
        """
        增强版元素查找和点击方法

        Args:
            selectors: 选择器列表
            description: 元素描述
            timeout: 超时时间
            retry_count: 重试次数

        Returns:
            bool: 点击是否成功
        """
        if timeout is None:
            timeout = self.website_config['timeout']

        self.logger.info(f"正在查找并点击: {description}")

        for attempt in range(retry_count):
            for i, selector in enumerate(selectors):
                try:
                    self.logger.debug(f"尝试选择器 {i+1}/{len(selectors)}: {selector}")

                    # 等待元素存在
                    element = WebDriverWait(self.driver, timeout).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )

                    # 等待元素可见
                    WebDriverWait(self.driver, 5).until(
                        EC.visibility_of(element)
                    )

                    # 等待元素可点击
                    element = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )

                    # 滚动到元素位置
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    time.sleep(self.login_flow_config['click_delay'])

                    # 高亮元素（调试用）
                    if not self.headless:
                        self._highlight_element(element)

                    # 尝试多种点击方式
                    click_success = False

                    # 方式1: 普通点击
                    try:
                        element.click()
                        click_success = True
                        self.logger.debug("普通点击成功")
                    except ElementClickInterceptedException:
                        self.logger.debug("普通点击被拦截，尝试其他方式")

                    # 方式2: JavaScript点击
                    if not click_success:
                        try:
                            self.driver.execute_script("arguments[0].click();", element)
                            click_success = True
                            self.logger.debug("JavaScript点击成功")
                        except Exception as e:
                            self.logger.debug(f"JavaScript点击失败: {e}")

                    # 方式3: ActionChains点击
                    if not click_success:
                        try:
                            actions = ActionChains(self.driver)
                            actions.move_to_element(element).click().perform()
                            click_success = True
                            self.logger.debug("ActionChains点击成功")
                        except Exception as e:
                            self.logger.debug(f"ActionChains点击失败: {e}")

                    if click_success:
                        self.logger.info(f"成功点击: {description}")
                        time.sleep(self.login_flow_config['step_delay'])
                        return True

                except TimeoutException:
                    self.logger.debug(f"选择器超时: {selector}")
                    continue
                except StaleElementReferenceException:
                    self.logger.debug(f"元素引用过期，重试: {selector}")
                    continue
                except Exception as e:
                    self.logger.debug(f"选择器失败 {selector}: {e}")
                    continue

            if attempt < retry_count - 1:
                self.logger.warning(f"第{attempt + 1}次尝试失败，等待后重试...")
                time.sleep(self.error_handling_config['error_retry_delay'])

        self.logger.error(f"无法找到或点击: {description}")
        self.stats['errors_encountered'] += 1
        self._handle_error(f"无法点击元素: {description}")
        return False

    def _highlight_element(self, element, duration: float = 1.0):
        """高亮显示元素（调试用）"""
        try:
            original_style = element.get_attribute("style")
            self.driver.execute_script(
                "arguments[0].style.border='3px solid red'; arguments[0].style.backgroundColor='yellow';",
                element
            )
            time.sleep(duration)
            self.driver.execute_script(f"arguments[0].style='{original_style}';", element)
        except:
            pass

    def input_credentials_enhanced(self) -> bool:
        """
        增强版登录凭据输入

        Returns:
            bool: 输入是否成功
        """
        try:
            # 获取用户名
            if not self.username:
                self.username = input("请输入用户名: ").strip()

            # 输入用户名
            self.logger.info("正在输入用户名...")
            username_element = self._find_input_element(
                self.selectors['username_field'],
                "用户名输入框"
            )

            if not username_element:
                return False

            self._safe_input(username_element, self.username, "用户名")

            # 获取密码
            if not self.password:
                import getpass
                self.password = getpass.getpass("请输入密码: ")

            # 输入密码
            self.logger.info("正在输入密码...")
            password_element = self._find_input_element(
                self.selectors['password_field'],
                "密码输入框"
            )

            if not password_element:
                return False

            self._safe_input(password_element, self.password, "密码")

            self.logger.info("成功输入登录凭据")
            self.stats['steps_completed'] += 1
            return True

        except Exception as e:
            self.logger.error(f"输入登录凭据失败: {e}")
            self.stats['errors_encountered'] += 1
            self._handle_error(f"凭据输入失败: {e}")
            return False

    def _find_input_element(self, selectors: List[str], description: str):
        """查找输入元素"""
        for selector in selectors:
            try:
                element = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                if element.is_displayed() and element.is_enabled():
                    return element
            except TimeoutException:
                continue

        self.logger.error(f"无法找到{description}")
        return None

    def _safe_input(self, element, text: str, field_name: str):
        """安全输入文本"""
        try:
            # 清空输入框
            element.clear()
            time.sleep(0.2)

            # 逐字符输入（模拟人类输入）
            for char in text:
                element.send_keys(char)
                time.sleep(self.login_flow_config['input_delay'])

            # 验证输入
            input_value = element.get_attribute('value')
            if input_value != text:
                self.logger.warning(f"{field_name}输入验证失败，重新输入")
                element.clear()
                element.send_keys(text)

        except Exception as e:
            self.logger.error(f"{field_name}输入失败: {e}")
            raise

    def click_annual_inspection_enhanced(self) -> bool:
        """
        增强版点击年审申请（处理二级菜单）

        Returns:
            bool: 点击是否成功
        """
        try:
            self.logger.info("正在处理年审申请二级菜单...")

            # 首先尝试直接点击年审申请（如果已经展开）
            self.logger.info("尝试直接点击年审申请...")
            if self.find_and_click_element_enhanced(
                self.selectors['annual_inspection'],
                "年审申请选项",
                timeout=5,
                retry_count=1
            ):
                return True

            # 如果直接点击失败，尝试先展开父菜单
            self.logger.info("直接点击失败，尝试展开父菜单...")

            # 查找并点击"网上年审"父菜单来展开二级菜单
            parent_menu_selectors = [
                "//div[@class='vehicleLeft into']//a[@class='block_left block_left1']",
                "//a[.//p[contains(text(), '网上年审')]]",
                "//a[@class='block_left block_left1']",
                "//div[@class='vehicleLeft into']//a[contains(@class, 'block_left')]",
                "//a[contains(@class, 'block_left') and .//p[contains(text(), '网上年审')]]"
            ]

            parent_clicked = False
            for attempt in range(3):  # 最多尝试3次
                for selector in parent_menu_selectors:
                    try:
                        self.logger.debug(f"尝试父菜单选择器: {selector}")

                        parent_element = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )

                        # 确保元素可见
                        WebDriverWait(self.driver, 3).until(
                            EC.visibility_of(parent_element)
                        )

                        # 滚动到元素位置
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", parent_element)
                        time.sleep(self.login_flow_config['click_delay'])

                        # 高亮元素（调试用）
                        if not self.headless:
                            self._highlight_element(parent_element)

                        # 尝试多种点击方式
                        click_success = False

                        # 方式1: 普通点击
                        try:
                            parent_element.click()
                            click_success = True
                            self.logger.debug("父菜单普通点击成功")
                        except ElementClickInterceptedException:
                            self.logger.debug("父菜单普通点击被拦截")

                        # 方式2: JavaScript点击
                        if not click_success:
                            try:
                                self.driver.execute_script("arguments[0].click();", parent_element)
                                click_success = True
                                self.logger.debug("父菜单JavaScript点击成功")
                            except Exception as e:
                                self.logger.debug(f"父菜单JavaScript点击失败: {e}")

                        # 方式3: ActionChains点击
                        if not click_success:
                            try:
                                actions = ActionChains(self.driver)
                                actions.move_to_element(parent_element).click().perform()
                                click_success = True
                                self.logger.debug("父菜单ActionChains点击成功")
                            except Exception as e:
                                self.logger.debug(f"父菜单ActionChains点击失败: {e}")

                        if click_success:
                            self.logger.info("成功点击父菜单，等待二级菜单展开...")
                            time.sleep(self.login_flow_config['step_delay'])  # 等待菜单展开动画
                            parent_clicked = True
                            break

                    except TimeoutException:
                        self.logger.debug(f"父菜单选择器超时: {selector}")
                        continue
                    except Exception as e:
                        self.logger.debug(f"点击父菜单失败 {selector}: {e}")
                        continue

                if parent_clicked:
                    break

                if attempt < 2:
                    self.logger.warning(f"父菜单点击失败，等待后重试 (尝试 {attempt + 1}/3)...")
                    time.sleep(self.error_handling_config['error_retry_delay'])

            if not parent_clicked:
                self.logger.error("无法点击父菜单来展开二级菜单")
                self.stats['errors_encountered'] += 1
                self._handle_error("无法展开年审申请父菜单")
                return False

            # 父菜单点击后，再次尝试点击年审申请
            self.logger.info("父菜单已展开，再次尝试点击年审申请...")
            time.sleep(1)  # 额外等待确保菜单完全展开

            if self.find_and_click_element_enhanced(
                self.selectors['annual_inspection'],
                "年审申请选项",
                retry_count=3
            ):
                self.logger.info("成功点击年审申请选项")
                return True
            else:
                self.logger.error("展开父菜单后仍无法点击年审申请")
                self.stats['errors_encountered'] += 1
                self._handle_error("展开菜单后无法点击年审申请")
                return False

        except Exception as e:
            self.logger.error(f"处理年审申请二级菜单失败: {e}")
            self.stats['errors_encountered'] += 1
            self._handle_error(f"年审申请菜单处理异常: {e}")
            return False

    def handle_captcha_enhanced(self) -> bool:
        """
        增强版CAPTCHA验证码处理

        Returns:
            bool: CAPTCHA处理是否成功
        """
        max_attempts = self.captcha_config['max_retry_attempts']

        for attempt in range(max_attempts):
            try:
                self.logger.info(f"正在处理CAPTCHA验证码 (尝试 {attempt + 1}/{max_attempts})...")
                self.stats['captcha_attempts'] += 1

                # 查找CAPTCHA图像
                captcha_element = self._find_captcha_element()

                if not captcha_element:
                    self.logger.warning("未找到CAPTCHA图像，可能不需要验证码")
                    return True

                # 获取页面HTML内容
                page_html = self.driver.page_source

                # 使用captcha_solver解决验证码
                self.logger.info("正在使用CAPTCHA解决器分析验证码...")
                result = captcha_solver.solve_captcha_from_html(
                    page_html,
                    use_gpu=self.captcha_config['use_gpu'],
                    similarity_threshold=self.captcha_config['similarity_threshold'],
                    save_debug_images=self.captcha_config['save_debug_images'],
                    verbose=self.captcha_config['verbose']
                )

                if not result['success']:
                    error_msg = result.get('error', '未知错误')
                    self.logger.error(f"CAPTCHA解决失败: {error_msg}")

                    if attempt < max_attempts - 1:
                        self.logger.info("等待后重试...")
                        time.sleep(self.captcha_config['retry_delay'])
                        continue
                    else:
                        return False

                # 获取点击坐标
                coordinates = result['coordinates']
                required_chars = result['required_chars']

                self.logger.info(f"CAPTCHA解决成功! 需要点击字符: {required_chars}")
                self.logger.info(f"点击坐标: {coordinates}")

                # 执行点击操作
                success = self._execute_captcha_clicks(captcha_element, coordinates, required_chars)

                if success:
                    self.logger.info("CAPTCHA验证码处理完成")
                    self.stats['steps_completed'] += 1
                    return True
                else:
                    if attempt < max_attempts - 1:
                        self.logger.warning("CAPTCHA点击失败，重试...")
                        time.sleep(self.captcha_config['retry_delay'])
                        continue
                    else:
                        return False

            except Exception as e:
                self.logger.error(f"处理CAPTCHA验证码失败: {e}")
                self.stats['errors_encountered'] += 1

                if attempt < max_attempts - 1:
                    self.logger.info("等待后重试...")
                    time.sleep(self.captcha_config['retry_delay'])
                    continue
                else:
                    self._handle_error(f"CAPTCHA处理失败: {e}")
                    return False

        return False

    def _find_captcha_element(self):
        """查找CAPTCHA元素"""
        for selector in self.selectors['captcha_image']:
            try:
                element = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                if element.is_displayed():
                    return element
            except TimeoutException:
                continue
        return None

    def _execute_captcha_clicks(self, captcha_element, coordinates, required_chars) -> bool:
        """执行CAPTCHA点击操作"""
        try:
            actions = ActionChains(self.driver)

            for i, coord in enumerate(coordinates):
                if coord:
                    x, y = coord
                    char = required_chars[i] if i < len(required_chars) else f"位置{i+1}"

                    self.logger.info(f"点击第{i+1}个字符 '{char}' 坐标: ({x}, {y})")

                    # 相对于CAPTCHA图像的点击
                    actions.move_to_element_with_offset(captcha_element, x, y).click().perform()
                    time.sleep(self.login_flow_config['click_delay'])
                else:
                    self.logger.warning(f"第{i+1}个字符坐标缺失，跳过")

            return True

        except Exception as e:
            self.logger.error(f"执行CAPTCHA点击失败: {e}")
            return False

    def complete_login_enhanced(self) -> bool:
        """
        增强版登录完成

        Returns:
            bool: 登录是否成功
        """
        try:
            self.logger.info("正在提交登录表单...")

            # 点击登录按钮
            if not self.find_and_click_element_enhanced(
                self.selectors['login_button'],
                "登录按钮"
            ):
                return False

            # 等待登录结果
            time.sleep(self.login_flow_config['step_delay'])

            # 检查登录结果
            login_result = self._check_login_result()

            if login_result['success']:
                self.logger.info("登录成功!")
                self.logger.info(f"当前页面: {self.driver.title}")
                self.logger.info(f"当前URL: {self.driver.current_url}")
                self.stats['steps_completed'] += 1
                return True
            else:
                self.logger.error(f"登录失败: {login_result['message']}")
                self.stats['errors_encountered'] += 1
                self._handle_error(f"登录失败: {login_result['message']}")
                return False

        except Exception as e:
            self.logger.error(f"完成登录失败: {e}")
            self.stats['errors_encountered'] += 1
            self._handle_error(f"登录完成失败: {e}")
            return False

    def _check_login_result(self) -> Dict[str, Any]:
        """检查登录结果"""
        try:
            current_url = self.driver.current_url
            page_title = self.driver.title

            # 检查URL和标题中的成功指示器
            url_success = any(
                keyword in current_url.lower()
                for keyword in self.success_check_config['url_keywords']
            )

            title_success = any(
                keyword in page_title.lower()
                for keyword in self.success_check_config['title_keywords']
            )

            # 检查页面元素指示器
            element_success = False
            for selector in self.success_check_config['element_indicators']:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.is_displayed():
                        element_success = True
                        break
                except NoSuchElementException:
                    continue

            # 检查错误信息
            error_message = self._check_error_messages()

            if error_message:
                return {
                    'success': False,
                    'message': error_message
                }

            # 综合判断
            if url_success or title_success or element_success:
                return {
                    'success': True,
                    'message': '登录成功'
                }
            else:
                return {
                    'success': False,
                    'message': '登录状态不明确'
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'检查登录结果时出错: {e}'
            }

    def _check_error_messages(self) -> str:
        """检查错误信息"""
        for selector in self.selectors['error_messages']:
            try:
                error_element = self.driver.find_element(By.XPATH, selector)
                if error_element.is_displayed():
                    return error_element.text.strip()
            except NoSuchElementException:
                continue
        return ""

    def _handle_error(self, error_message: str):
        """处理错误"""
        if self.error_handling_config['screenshot_on_error']:
            self.take_screenshot(f"error_{int(time.time())}")

        if self.error_handling_config['save_page_source']:
            self._save_page_source(f"error_{int(time.time())}")

    def take_screenshot(self, filename: str = None) -> str:
        """
        截取当前页面截图

        Args:
            filename: 截图文件名

        Returns:
            str: 截图文件路径
        """
        try:
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}"

            if not filename.endswith('.png'):
                filename += '.png'

            screenshot_path = os.path.join(os.getcwd(), filename)
            self.driver.save_screenshot(screenshot_path)
            self.logger.info(f"截图已保存: {screenshot_path}")
            self.stats['screenshots_taken'] += 1
            return screenshot_path

        except Exception as e:
            self.logger.error(f"截图失败: {e}")
            return ""

    def _save_page_source(self, filename: str):
        """保存页面源码"""
        try:
            if not filename.endswith('.html'):
                filename += '.html'

            source_path = os.path.join(os.getcwd(), filename)
            with open(source_path, 'w', encoding='utf-8') as f:
                f.write(self.driver.page_source)

            self.logger.info(f"页面源码已保存: {source_path}")

        except Exception as e:
            self.logger.error(f"保存页面源码失败: {e}")

    def run_automation_enhanced(self) -> bool:
        """
        运行增强版自动化登录流程

        Returns:
            bool: 自动化是否成功
        """
        self.stats['start_time'] = time.time()

        try:
            self.logger.info("开始增强版交通运输网站自动化登录流程")
            self.logger.info("=" * 80)

            # 步骤1: 设置WebDriver
            self.logger.info("步骤1: 设置WebDriver")
            if not self.setup_driver():
                return False

            # 步骤2: 导航到网站
            self.logger.info("步骤2: 导航到网站")
            if not self.navigate_to_website():
                return False

            # 步骤3: 点击"营运车辆"
            self.logger.info("步骤3: 点击营运车辆菜单")
            if not self.find_and_click_element_enhanced(
                self.selectors['commercial_vehicles'],
                "营运车辆菜单"
            ):
                return False

            # 步骤4: 点击"年审申请" (处理二级菜单)
            self.logger.info("步骤4: 点击年审申请选项")
            if not self.click_annual_inspection_enhanced():
                return False

            # 步骤5: 点击"法人用户登录"
            self.logger.info("步骤5: 点击法人用户登录")
            if not self.find_and_click_element_enhanced(
                self.selectors['corporate_login'],
                "法人用户登录"
            ):
                return False

            # 步骤6: 输入登录凭据
            self.logger.info("步骤6: 输入登录凭据")
            if not self.input_credentials_enhanced():
                return False

            # 步骤7: 处理CAPTCHA验证码
            self.logger.info("步骤7: 处理CAPTCHA验证码")
            if not self.handle_captcha_enhanced():
                return False

            # 步骤8: 完成登录
            self.logger.info("步骤8: 完成登录")
            if not self.complete_login_enhanced():
                return False

            self.logger.info("增强版自动化登录流程完成!")
            self._print_statistics()
            return True

        except Exception as e:
            self.logger.error(f"自动化流程失败: {e}")
            self.stats['errors_encountered'] += 1
            self._handle_error(f"自动化流程异常: {e}")
            return False
        finally:
            self.stats['end_time'] = time.time()

            # 可选：保持浏览器打开以供用户查看结果
            if self.login_flow_config['keep_browser_open'] and not self.headless:
                input("按Enter键关闭浏览器...")

            self.cleanup()

    def _print_statistics(self):
        """打印统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time'] if self.stats['end_time'] else 0

        self.logger.info("=" * 80)
        self.logger.info("自动化统计信息:")
        self.logger.info(f"总耗时: {duration:.2f}秒")
        self.logger.info(f"完成步骤: {self.stats['steps_completed']}")
        self.logger.info(f"遇到错误: {self.stats['errors_encountered']}")
        self.logger.info(f"截图次数: {self.stats['screenshots_taken']}")
        self.logger.info(f"CAPTCHA尝试: {self.stats['captcha_attempts']}")
        self.logger.info("=" * 80)

    def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                self.driver.quit()
                self.logger.info("WebDriver已关闭")
        except Exception as e:
            self.logger.warning(f"清理资源时出错: {e}")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="增强版中国政府交通运输网站自动化登录工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python enhanced_transportation_login.py
  python enhanced_transportation_login.py --username your_username --password your_password
  python enhanced_transportation_login.py --headless
  python enhanced_transportation_login.py --config custom_config.json
        """
    )

    parser.add_argument('--username', '-u', type=str, default='',
                       help='登录用户名')
    parser.add_argument('--password', '-p', type=str, default='',
                       help='登录密码')
    parser.add_argument('--headless', action='store_true',
                       help='使用无头模式运行')
    parser.add_argument('--config', '-c', type=str,
                       help='自定义配置文件路径 (JSON格式)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细日志')
    parser.add_argument('--screenshot', '-s', action='store_true',
                       help='在关键步骤截图')

    return parser.parse_args()


def load_config_file(config_path: str) -> Dict:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return {}


def main():
    """主函数"""
    print("🚗 增强版中国政府交通运输网站自动化登录工具")
    print("=" * 80)

    # 解析命令行参数
    args = parse_arguments()

    # 加载配置覆盖
    config_override = {}
    if args.config:
        config_override = load_config_file(args.config)

    # 获取用户输入（如果命令行未提供）
    username = args.username or input("请输入用户名 (留空稍后输入): ").strip()
    password = args.password or input("请输入密码 (留空稍后输入): ").strip()

    try:
        # 创建增强版自动化器实例
        automator = EnhancedTransportationLoginAutomator(
            username=username,
            password=password,
            headless=args.headless,
            config_override=config_override
        )

        # 运行自动化流程
        success = automator.run_automation_enhanced()

        if success:
            print("\n✅ 增强版自动化登录成功!")
        else:
            print("\n❌ 增强版自动化登录失败，请查看日志了解详情")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        logging.error(f"程序异常: {e}", exc_info=True)


if __name__ == "__main__":
    main()

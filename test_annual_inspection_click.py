#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
年审申请点击测试脚本
专门测试二级菜单的年审申请点击逻辑
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AnnualInspectionTester:
    """年审申请点击测试器"""
    
    def __init__(self, headless=False):
        self.headless = headless
        self.driver = None
        self.wait = None
        
        # 年审申请相关选择器
        self.annual_inspection_selectors = [
            "//a[@id='twoChild1' and contains(@onclick, '年度审验申请')]",
            "//a[contains(@onclick, 'jumpToHandle') and contains(@onclick, '1001')]",
            "//a[@class='twoChild' and .//p[contains(text(), '年审申请')]]",
            "//a[contains(@onclick, '普通货运车辆网上年度审验申请')]",
            "//div[@class='vehicleLeft into']//a[.//p[contains(text(), '年审申请')]]",
            "//li[@class='block_left2']//a[.//p[contains(text(), '年审申请')]]"
        ]
        
        # 父菜单选择器
        self.parent_menu_selectors = [
            "//div[@class='vehicleLeft into']//a[@class='block_left block_left1']",
            "//a[.//p[contains(text(), '网上年审')]]",
            "//a[@class='block_left block_left1']",
            "//div[@class='vehicleLeft into']//a[contains(@class, 'block_left')]"
        ]
        
        # 营运车辆选择器
        self.commercial_vehicles_selectors = [
            "//a[contains(text(), '营运车辆')]",
            "//span[contains(text(), '营运车辆')]",
            "//div[contains(text(), '营运车辆')]",
            "//li[contains(text(), '营运车辆')]"
        ]
    
    def setup_driver(self):
        """设置WebDriver"""
        try:
            logger.info("设置Chrome WebDriver...")
            
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, 30)
            
            logger.info("WebDriver设置成功")
            return True
            
        except Exception as e:
            logger.error(f"WebDriver设置失败: {e}")
            return False
    
    def navigate_to_website(self):
        """导航到网站"""
        try:
            url = "https://ysfw.mot.gov.cn/NetRoadCGSS-web/"
            logger.info(f"导航到: {url}")
            
            self.driver.get(url)
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            logger.info(f"页面标题: {self.driver.title}")
            return True
            
        except Exception as e:
            logger.error(f"导航失败: {e}")
            return False
    
    def click_commercial_vehicles(self):
        """点击营运车辆"""
        logger.info("点击营运车辆...")
        
        for selector in self.commercial_vehicles_selectors:
            try:
                element = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)
                element.click()
                logger.info("成功点击营运车辆")
                time.sleep(3)  # 等待页面加载
                return True
            except TimeoutException:
                continue
            except Exception as e:
                logger.debug(f"选择器失败 {selector}: {e}")
                continue
        
        logger.error("无法点击营运车辆")
        return False
    
    def test_direct_annual_inspection_click(self):
        """测试直接点击年审申请"""
        logger.info("测试直接点击年审申请...")
        
        for i, selector in enumerate(self.annual_inspection_selectors):
            try:
                logger.info(f"尝试选择器 {i+1}: {selector}")
                
                # 检查元素是否存在
                elements = self.driver.find_elements(By.XPATH, selector)
                logger.info(f"找到 {len(elements)} 个匹配元素")
                
                if elements:
                    element = elements[0]
                    logger.info(f"元素可见: {element.is_displayed()}")
                    logger.info(f"元素启用: {element.is_enabled()}")
                    
                    if element.is_displayed():
                        # 尝试点击
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                        time.sleep(1)
                        element.click()
                        logger.info(f"✅ 成功使用选择器 {i+1} 点击年审申请")
                        return True
                    else:
                        logger.info(f"元素不可见，可能需要展开父菜单")
                
            except Exception as e:
                logger.debug(f"选择器 {i+1} 失败: {e}")
                continue
        
        logger.warning("直接点击年审申请失败")
        return False
    
    def test_parent_menu_expansion(self):
        """测试父菜单展开"""
        logger.info("测试父菜单展开...")
        
        for i, selector in enumerate(self.parent_menu_selectors):
            try:
                logger.info(f"尝试父菜单选择器 {i+1}: {selector}")
                
                # 检查父菜单元素
                elements = self.driver.find_elements(By.XPATH, selector)
                logger.info(f"找到 {len(elements)} 个父菜单元素")
                
                if elements:
                    element = elements[0]
                    logger.info(f"父菜单可见: {element.is_displayed()}")
                    
                    if element.is_displayed():
                        # 点击父菜单
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                        time.sleep(1)
                        element.click()
                        logger.info(f"✅ 成功点击父菜单 {i+1}")
                        time.sleep(3)  # 等待菜单展开
                        return True
                
            except Exception as e:
                logger.debug(f"父菜单选择器 {i+1} 失败: {e}")
                continue
        
        logger.error("无法点击父菜单")
        return False
    
    def test_annual_inspection_after_expansion(self):
        """测试父菜单展开后点击年审申请"""
        logger.info("测试父菜单展开后点击年审申请...")
        
        # 等待菜单展开
        time.sleep(2)
        
        for i, selector in enumerate(self.annual_inspection_selectors):
            try:
                logger.info(f"展开后尝试选择器 {i+1}: {selector}")
                
                # 等待元素出现
                element = WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                
                logger.info(f"元素可见: {element.is_displayed()}")
                logger.info(f"元素启用: {element.is_enabled()}")
                
                if element.is_displayed() and element.is_enabled():
                    # 尝试点击
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(1)
                    element.click()
                    logger.info(f"✅ 成功使用选择器 {i+1} 点击年审申请")
                    return True
                
            except TimeoutException:
                logger.debug(f"选择器 {i+1} 超时")
                continue
            except Exception as e:
                logger.debug(f"选择器 {i+1} 失败: {e}")
                continue
        
        logger.error("展开后仍无法点击年审申请")
        return False
    
    def analyze_page_structure(self):
        """分析页面结构"""
        logger.info("分析页面结构...")
        
        try:
            # 查找所有可能相关的元素
            logger.info("查找包含'年审'的元素...")
            annual_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '年审')]")
            logger.info(f"找到 {len(annual_elements)} 个包含'年审'的元素")
            
            for i, elem in enumerate(annual_elements[:5]):  # 只显示前5个
                try:
                    logger.info(f"元素 {i+1}: {elem.tag_name} - {elem.text[:50]}...")
                    logger.info(f"  可见: {elem.is_displayed()}")
                    logger.info(f"  类名: {elem.get_attribute('class')}")
                    logger.info(f"  ID: {elem.get_attribute('id')}")
                except:
                    pass
            
            # 查找vehicleLeft类的元素
            logger.info("查找vehicleLeft相关元素...")
            vehicle_elements = self.driver.find_elements(By.XPATH, "//*[contains(@class, 'vehicleLeft')]")
            logger.info(f"找到 {len(vehicle_elements)} 个vehicleLeft元素")
            
            # 保存页面源码用于分析
            with open("page_analysis.html", "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            logger.info("页面源码已保存到 page_analysis.html")
            
        except Exception as e:
            logger.error(f"页面结构分析失败: {e}")
    
    def take_screenshot(self, filename):
        """截图"""
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"截图已保存: {filename}")
        except Exception as e:
            logger.error(f"截图失败: {e}")
    
    def run_test(self):
        """运行完整测试"""
        try:
            logger.info("开始年审申请点击测试")
            logger.info("=" * 60)
            
            # 设置WebDriver
            if not self.setup_driver():
                return False
            
            # 导航到网站
            if not self.navigate_to_website():
                return False
            
            self.take_screenshot("step1_homepage.png")
            
            # 点击营运车辆
            if not self.click_commercial_vehicles():
                return False
            
            self.take_screenshot("step2_after_commercial_vehicles.png")
            
            # 分析页面结构
            self.analyze_page_structure()
            
            # 测试直接点击年审申请
            if self.test_direct_annual_inspection_click():
                logger.info("✅ 直接点击年审申请成功!")
                self.take_screenshot("step3_annual_inspection_success.png")
                return True
            
            # 如果直接点击失败，尝试展开父菜单
            logger.info("直接点击失败，尝试展开父菜单...")
            if not self.test_parent_menu_expansion():
                return False
            
            self.take_screenshot("step3_parent_menu_expanded.png")
            
            # 展开后再次尝试点击年审申请
            if self.test_annual_inspection_after_expansion():
                logger.info("✅ 展开父菜单后点击年审申请成功!")
                self.take_screenshot("step4_annual_inspection_success.png")
                return True
            else:
                logger.error("❌ 所有方法都失败了")
                self.take_screenshot("step4_all_failed.png")
                return False
            
        except Exception as e:
            logger.error(f"测试过程异常: {e}")
            return False
        finally:
            if not self.headless:
                input("按Enter键关闭浏览器...")
            if self.driver:
                self.driver.quit()


def main():
    """主函数"""
    print("🧪 年审申请点击测试工具")
    print("=" * 50)
    
    headless = input("是否使用无头模式? (y/N): ").strip().lower() in ['y', 'yes']
    
    tester = AnnualInspectionTester(headless=headless)
    success = tester.run_test()
    
    if success:
        print("\n✅ 测试成功!")
    else:
        print("\n❌ 测试失败，请查看日志和截图")


if __name__ == "__main__":
    main()

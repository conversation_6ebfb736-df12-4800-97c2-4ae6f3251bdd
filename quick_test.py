#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
用于快速验证自动化脚本的核心功能，特别是年审申请的点击逻辑
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from enhanced_transportation_login import EnhancedTransportationLoginAutomator
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保 enhanced_transportation_login.py 文件存在")
    sys.exit(1)


def quick_navigation_test():
    """快速导航测试 - 只测试到年审申请点击"""
    print("🚀 快速导航测试")
    print("测试范围: 网站导航 → 营运车辆 → 年审申请")
    print("-" * 60)
    
    # 自定义配置 - 专注于导航测试
    config_override = {
        'website_config': {
            'timeout': 45
        },
        'login_flow_config': {
            'step_delay': 3,
            'max_retries': 5,
            'screenshot_on_error': True,
            'keep_browser_open': True
        },
        'error_handling_config': {
            'screenshot_on_error': True,
            'save_page_source': True
        }
    }
    
    try:
        # 创建自动化器
        automator = EnhancedTransportationLoginAutomator(
            username="test_user",  # 虚拟用户名，不会用到
            password="test_pass",  # 虚拟密码，不会用到
            headless=False,        # 显示浏览器以便观察
            config_override=config_override
        )
        
        print("正在设置WebDriver...")
        if not automator.setup_driver():
            print("❌ WebDriver设置失败")
            return False
        
        print("正在导航到网站...")
        if not automator.navigate_to_website():
            print("❌ 网站导航失败")
            return False
        
        # 截图记录初始状态
        automator.take_screenshot("quick_test_1_homepage.png")
        
        print("正在点击营运车辆...")
        if not automator.find_and_click_element_enhanced(
            automator.selectors['commercial_vehicles'], 
            "营运车辆菜单"
        ):
            print("❌ 营运车辆点击失败")
            return False
        
        # 截图记录营运车辆页面
        automator.take_screenshot("quick_test_2_commercial_vehicles.png")
        
        print("正在点击年审申请（二级菜单）...")
        if not automator.click_annual_inspection_enhanced():
            print("❌ 年审申请点击失败")
            return False
        
        # 截图记录年审申请页面
        automator.take_screenshot("quick_test_3_annual_inspection.png")
        
        print("✅ 快速导航测试成功!")
        print("已成功导航到年审申请页面")
        
        # 保持浏览器打开以供检查
        input("按Enter键关闭浏览器...")
        
        return True
        
    except Exception as e:
        print(f"❌ 快速测试异常: {e}")
        return False
    finally:
        if 'automator' in locals() and automator.driver:
            automator.cleanup()


def quick_selector_test():
    """快速选择器测试 - 验证所有选择器是否有效"""
    print("\n🔍 快速选择器测试")
    print("测试所有关键元素的选择器有效性")
    print("-" * 60)
    
    try:
        from enhanced_transportation_login import EnhancedTransportationLoginAutomator
        
        # 创建自动化器实例来获取选择器
        automator = EnhancedTransportationLoginAutomator()
        
        print("营运车辆选择器:")
        for i, selector in enumerate(automator.selectors['commercial_vehicles'][:3], 1):
            print(f"  {i}. {selector}")
        
        print("\n年审申请选择器:")
        for i, selector in enumerate(automator.selectors['annual_inspection'][:5], 1):
            print(f"  {i}. {selector}")
        
        print("\n法人用户登录选择器:")
        for i, selector in enumerate(automator.selectors['corporate_login'][:3], 1):
            print(f"  {i}. {selector}")
        
        print("\n✅ 选择器配置检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 选择器测试异常: {e}")
        return False


def quick_config_test():
    """快速配置测试 - 验证配置文件加载"""
    print("\n⚙️ 快速配置测试")
    print("验证配置文件是否正确加载")
    print("-" * 60)
    
    try:
        from config import *
        
        print("网站配置:")
        print(f"  URL: {WEBSITE_CONFIG['base_url']}")
        print(f"  超时: {WEBSITE_CONFIG['timeout']}秒")
        
        print("\nCAPTCHA配置:")
        print(f"  相似度阈值: {CAPTCHA_CONFIG['similarity_threshold']}")
        print(f"  最大重试: {CAPTCHA_CONFIG['max_retry_attempts']}")
        print(f"  保存调试图像: {CAPTCHA_CONFIG['save_debug_images']}")
        
        print("\n登录流程配置:")
        print(f"  步骤延迟: {LOGIN_FLOW_CONFIG['step_delay']}秒")
        print(f"  最大重试: {LOGIN_FLOW_CONFIG['max_retries']}")
        
        print("\n✅ 配置文件加载正常")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试异常: {e}")
        return False


def main():
    """主函数"""
    print("⚡ 交通运输网站自动化快速测试工具")
    print("=" * 80)
    
    tests = [
        ("配置文件测试", quick_config_test),
        ("选择器测试", quick_selector_test),
        ("导航功能测试", quick_navigation_test)
    ]
    
    print("可用测试:")
    for i, (name, _) in enumerate(tests, 1):
        print(f"  {i}. {name}")
    
    print("\n选择要运行的测试:")
    print("  0. 运行所有测试")
    print("  1-3. 运行特定测试")
    print("  q. 退出")
    
    try:
        choice = input("\n请输入选择 (0-3 或 q): ").strip().lower()
        
        if choice == 'q':
            print("👋 再见!")
            return
        
        if choice == '0':
            print("\n🚀 运行所有测试...")
            results = []
            
            for name, test_func in tests:
                print(f"\n{'='*20} {name} {'='*20}")
                try:
                    result = test_func()
                    results.append((name, result))
                except KeyboardInterrupt:
                    print(f"\n⚠️ 用户中断了 {name}")
                    break
                except Exception as e:
                    print(f"\n❌ {name} 运行异常: {e}")
                    results.append((name, False))
            
            print("\n" + "="*80)
            print("📊 所有测试结果:")
            for name, result in results:
                status = "✅ 通过" if result else "❌ 失败"
                print(f"  {name}: {status}")
        
        elif choice.isdigit() and 1 <= int(choice) <= len(tests):
            index = int(choice) - 1
            name, test_func = tests[index]
            print(f"\n🚀 运行测试: {name}")
            print("="*80)
            
            try:
                result = test_func()
                status = "✅ 通过" if result else "❌ 失败"
                print(f"\n📊 测试结果: {status}")
            except KeyboardInterrupt:
                print(f"\n⚠️ 用户中断了测试")
            except Exception as e:
                print(f"\n❌ 测试运行异常: {e}")
        
        else:
            print("❌ 无效选择")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
    
    print("\n🎉 快速测试完成!")


if __name__ == "__main__":
    main()
